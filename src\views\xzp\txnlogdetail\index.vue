<template>
  <div class="app-container">
    <el-card class="search-form">
      <el-form :inline="true" :model="search" :rules="rules" ref="searchForm" class="search-form" label-width="100px" size="small">
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="起始日期" prop="startTime">
              <el-date-picker
                style="width: 200px;"
                v-model="search.startTime"
                type="date"
                value-format="yyyyMMdd"
                placeholder="请选择起始日期"
                clearable
                @change="validateDateRange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="终止日期" prop="endTime">
              <el-date-picker
                style="width: 200px;"
                v-model="search.endTime"
                type="date"
                value-format="yyyyMMdd"
                placeholder="请选择终止日期"
                clearable
                @change="validateDateRange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="search-btns">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
            <el-button type="success" icon="el-icon-download" @click="handleExport" :disabled="!data.length">导出</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据为空时的提示信息 -->
    <el-alert
      v-if="!data.length && !loading && !hasSearched"
      title="请设置查询条件后点击查询按钮获取数据"
      type="info"
      :closable="false"
      show-icon
      style="margin-bottom: 20px;"
    />

    <!-- 查询结果表格 -->
    <el-card v-if="data.length || loading">
      <div slot="header" class="clearfix">
        <span>缴费明细查询结果</span>
        <span class="total-info">共 {{ total }} 条记录</span>
      </div>
      
      <yoaf-table
        :data="data"
        :loading="loading"
        :columns="columns"
        :pagination="pagination"
        @pagination="handlePagination"
        stripe
        border
        height="500"
      />
    </el-card>
  </div>
</template>

<script>
import { queryTxnLogDetail, exportTxnLogDetail } from '@/api/xzp/inttxnlog'
import { formatDate } from '@/utils'

export default {
  name: 'TxnLogDetail',
  data() {
    return {
      loading: false,
      hasSearched: false,
      data: [],
      total: 0,
      search: {
        startTime: '',
        endTime: ''
      },
      rules: {
        startTime: [
          { required: true, message: '请选择起始日期', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择终止日期', trigger: 'change' }
        ]
      },
      pagination: {
        pageNum: 1,
        pageSize: 20
      },
      columns: [
        {
          prop: 'setdate',
          label: '日期',
          width: 120,
          align: 'center'
        },
        {
          prop: 'str',
          label: '业务代码_商户号',
          width: 200,
          align: 'center'
        },
        {
          prop: 'str30',
          label: '子业务代码',
          width: 120,
          align: 'center'
        },
        {
          prop: 'str31',
          label: '业务名称',
          width: 200,
          align: 'left'
        },
        {
          prop: 'num',
          label: '笔数',
          width: 100,
          align: 'right',
          formatter: (row, column, cellValue) => {
            return cellValue ? Number(cellValue).toLocaleString() : '0'
          }
        },
        {
          prop: 'amt',
          label: '金额(元)',
          width: 150,
          align: 'right',
          formatter: (row, column, cellValue) => {
            return cellValue ? (Number(cellValue) / 100).toFixed(2) : '0.00'
          }
        }
      ]
    }
  },
  methods: {
    // 查询数据
    handleQuery() {
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          this.pagination.pageNum = 1
          this.getList()
        }
      })
    },
    
    // 获取列表数据
    async getList() {
      this.loading = true
      this.hasSearched = true
      try {
        const response = await queryTxnLogDetail(
          this.search,
          this.pagination.pageNum,
          this.pagination.pageSize
        )
        if (response.code === 200) {
          this.data = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || '查询失败')
          this.data = []
          this.total = 0
        }
      } catch (error) {
        console.error('查询缴费明细失败:', error)
        this.$message.error('查询失败，请稍后重试')
        this.data = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },
    
    // 重置查询
    resetQuery() {
      this.$refs.searchForm.resetFields()
      this.search = {
        startTime: '',
        endTime: ''
      }
      this.data = []
      this.total = 0
      this.hasSearched = false
      this.pagination.pageNum = 1
    },
    
    // 分页处理
    handlePagination(pagination) {
      this.pagination = pagination
      this.getList()
    },
    
    // 日期范围验证
    validateDateRange() {
      if (this.search.startTime && this.search.endTime) {
        const start = new Date(this.search.startTime.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3'))
        const end = new Date(this.search.endTime.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3'))

        if (start > end) {
          this.$message.warning('起始日期不能大于终止日期')
          this.search.endTime = ''
          return
        }
      }
    },
    
    // 导出功能
    handleExport() {
      if (!this.data.length) {
        this.$message.warning('没有可导出的数据')
        return
      }
      
      this.$confirm('确认导出缴费明细数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.exportData()
      })
    },
    
    // 执行导出
    async exportData() {
      try {
        const params = {
          ...this.search
        }

        // 调用后端导出接口
        const response = await exportTxnLogDetail(params)
        this.handleExportData(response)
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请稍后重试')
      }
    },

    // 处理返回的流文件
    handleExportData(res) {
      if (!res) return
      let data = res.data
      let filename = '缴费明细列表.xls'

      // 尝试从响应头获取文件名
      if (res.headers && res.headers['content-disposition']) {
        const disposition = res.headers['content-disposition']
        const filenameMatch = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = decodeURI(filenameMatch[1].replace(/['"]/g, ''))
        }
      } else {
        // 如果没有从响应头获取到文件名，使用默认格式
        filename = `缴费明细_${this.search.startTime}_${this.search.endTime}_${formatDate(new Date(), 'yyyyMMddHHmmss')}.xls`
      }

      const link = document.createElement('a')
      // 创建 Blob对象 存储二进制文件
      let blob = new Blob([data], { type: 'application/x-excel' })
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$message.success('导出成功')
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
  
  .el-form {
    .el-row {
      margin-bottom: 10px;
    }
    
    .search-btns {
      text-align: center;
      margin-top: 10px;
    }
  }
}

.custom-input {
  width: 100%;
}

.total-info {
  float: right;
  color: #909399;
  font-size: 14px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>