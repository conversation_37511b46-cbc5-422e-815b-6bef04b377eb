# 缴费明细查询页面

## 功能概述

缴费明细查询页面用于查询和导出缴费交易的明细数据，支持多种查询条件和数据导出功能。

## 主要功能

### 1. 查询功能
- **起始日期/终止日期**：必填项，支持日期范围查询，最大查询范围31天
- **委托代码**：可选，用于筛选特定委托代码的交易
- **业务代码**：可选，用于筛选特定业务类型的交易
- **子业务代码**：可选，用于筛选特定子业务的交易
- **用户号**：可选，用于筛选特定用户的交易
- **卡号**：可选，用于筛选特定卡号的交易

### 2. 数据展示
- **日期**：交易发生日期
- **业务代码_商户号**：业务代码和商户号的组合
- **子业务代码**：具体的子业务类型
- **业务名称**：业务的中文名称
- **笔数**：交易笔数统计
- **金额(元)**：交易金额，自动转换为元单位显示

### 3. 导出功能
- 支持将查询结果导出为Excel文件
- 使用后端模板导出，确保格式统一
- 导出文件名包含查询日期范围和时间戳

## 技术实现

### 前端技术栈
- Vue.js 2.x
- Element UI
- yoaf-table 组件

### 后端接口
- **查询接口**：`POST /api/admin/xzp/txn/queryTxnLogDetail`
- **导出接口**：`POST /api/admin/xzp/txn/exportTxnLogDetail`

### 数据源
- 主要查询表：`tb_int_txn_log_temp`
- 关联表：`tb_merch_sub_ope`、`tb_merch_ope`

## 使用说明

1. **设置查询条件**
   - 必须选择起始日期和终止日期
   - 可根据需要设置其他筛选条件

2. **执行查询**
   - 点击"查询"按钮执行查询
   - 支持分页显示结果

3. **导出数据**
   - 查询到数据后，点击"导出"按钮
   - 系统将生成Excel文件供下载

## 注意事项

1. 查询日期范围不能超过31天
2. 起始日期不能大于终止日期
3. 导出功能需要先执行查询获取数据
4. 金额字段自动从分转换为元显示
5. 支持数据为空时的友好提示

## 文件结构

```
src/views/xzp/txnlogdetail/
├── index.vue          # 主页面组件
└── README.md          # 说明文档

src/api/xzp/
└── inttxnlog.js       # API接口定义
```

## 更新日志

- 2024-01-XX：完善查询条件，添加更多筛选字段
- 2024-01-XX：优化导出功能，使用后端模板导出
- 2024-01-XX：修复API路径匹配问题
- 2024-01-XX：优化页面布局和用户体验
