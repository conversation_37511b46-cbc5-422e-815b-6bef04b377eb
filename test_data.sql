-- 测试数据SQL - 缴费明细查询
-- 请按顺序执行以下SQL语句

-- 1. 首先插入商户操作表数据 (tb_merch_ope)
INSERT INTO tb_merch_ope (merch_id, ope_cd, prdt_nm) VALUES 
('350200050235', '101001', '水费缴费'),
('350200050235', '101002', '电费缴费'),
('350200050235', '101003', '燃气费缴费'),
('350200050238', '101001', '水费缴费'),
('350200050238', '101002', '电费缴费');

-- 2. 插入商户子操作表数据 (tb_merch_sub_ope)
INSERT INTO tb_merch_sub_ope (merch_id, ope_cd, sub_ope_id, sub_ope_nm) VALUES 
('350200050235', '198020', '3502001', '厦门水务'),
('350200050235', '198020', '3502002', '厦门电力'),
('350200050235', '198020', '3502003', '厦门燃气'),
('350200050238', '198020', '3502001', '厦门水务'),
('350200050238', '198020', '3502002', '厦门电力');

-- 3. 插入交易流水测试数据 (tb_int_txn_log_temp)
-- 注意：根据你的SQL，需要满足以下条件：
-- - merch_id in ('350200050235','350200050238')
-- - tran_stat_cd = '011'
-- - ope_cd = '198020' 或 ope_cd <> '198020'

-- 插入 ope_cd = '198020' 的数据（会关联 tb_merch_sub_ope）
INSERT INTO tb_int_txn_log_temp (
    local_dt, ope_cd, merch_id, pay_id, tran_at, tran_stat_cd, tran_cd, unite_clr_dt
) VALUES 
-- 2025年7月数据
('20250701', '198020', '350200050235', '3502001001', 10000, '011', 'TXN001', '20250701'),
('20250701', '198020', '350200050235', '3502001002', 15000, '011', 'TXN002', '20250701'),
('20250701', '198020', '350200050235', '3502002001', 20000, '011', 'TXN003', '20250701'),
('20250701', '198020', '350200050238', '3502001001', 12000, '011', 'TXN004', '20250701'),
('20250702', '198020', '350200050235', '3502001003', 8000, '011', 'TXN005', '20250702'),
('20250702', '198020', '350200050235', '3502002002', 25000, '011', 'TXN006', '20250702'),
('20250702', '198020', '350200050238', '3502002001', 18000, '011', 'TXN007', '20250702'),
('20250703', '198020', '350200050235', '3502003001', 30000, '011', 'TXN008', '20250703'),

-- 2025年7月更多数据
('20250704', '198020', '350200050235', '3502001004', 9500, '011', 'TXN009', '20250704'),
('20250704', '198020', '350200050238', '3502001002', 11000, '011', 'TXN010', '20250704'),
('20250705', '198020', '350200050235', '3502002003', 22000, '011', 'TXN011', '20250705'),
('20250705', '198020', '350200050238', '3502002002', 16000, '011', 'TXN012', '20250705'),

-- 2025年7月中旬数据
('20250715', '198020', '350200050235', '3502001005', 13000, '011', 'TXN013', '20250715'),
('20250715', '198020', '350200050235', '3502002004', 28000, '011', 'TXN014', '20250715'),
('20250716', '198020', '350200050238', '3502001003', 14500, '011', 'TXN015', '20250716'),
('20250716', '198020', '350200050235', '3502003002', 35000, '011', 'TXN016', '20250716');

-- 插入 ope_cd <> '198020' 的数据（会关联 tb_merch_ope）
-- 注意：这部分只针对 merch_id = '350200050235'
INSERT INTO tb_int_txn_log_temp (
    local_dt, ope_cd, merch_id, pay_id, tran_at, tran_stat_cd, tran_cd, unite_clr_dt
) VALUES 
('20250701', '101001', '350200050235', '', 5000, '011', 'TXN017', '20250701'),
('20250701', '101002', '350200050235', '', 7500, '011', 'TXN018', '20250701'),
('20250702', '101001', '350200050235', '', 6000, '011', 'TXN019', '20250702'),
('20250702', '101003', '350200050235', '', 8500, '011', 'TXN020', '20250702'),
('20250703', '101002', '350200050235', '', 9000, '011', 'TXN021', '20250703'),
('20250704', '101001', '350200050235', '', 5500, '011', 'TXN022', '20250704'),
('20250705', '101002', '350200050235', '', 8000, '011', 'TXN023', '20250705'),
('20250715', '101003', '350200050235', '', 12000, '011', 'TXN024', '20250715'),
('20250716', '101001', '350200050235', '', 6500, '011', 'TXN025', '20250716');

-- 4. 验证数据插入
-- 检查数据是否正确插入
SELECT '商户操作表数据' as table_name, COUNT(*) as count FROM tb_merch_ope WHERE merch_id IN ('350200050235', '350200050238')
UNION ALL
SELECT '商户子操作表数据', COUNT(*) FROM tb_merch_sub_ope WHERE merch_id IN ('350200050235', '350200050238')
UNION ALL
SELECT '交易流水表数据', COUNT(*) FROM tb_int_txn_log_temp WHERE merch_id IN ('350200050235', '350200050238') AND tran_stat_cd = '011';

-- 5. 测试查询（使用你提供的SQL结构）
-- 这个查询应该返回类似你截图中的数据
select
    q.local_dt as setdate,
    case
        when grouping(concat(q.ope_cd, '_', q.merch_id))= 0
        and grouping(q.pay_id)= 1 then '小计'
        else concat(q.ope_cd, '_', q.merch_id)
    end str,
    q.pay_id as str30,
    q.str31,
    count(q.tran_at) num,
    sum(q.tran_at) amt
from
    (
    select
        a.local_dt,
        a.ope_cd,
        a.merch_id,
        substring(a.pay_id, 1, 7) pay_id,  -- 注意：PostgreSQL使用1开始索引
        b.sub_ope_nm as str31,
        a.tran_at,
        a.tran_cd
    from
        tb_int_txn_log_temp a,
        tb_merch_sub_ope b
    where
        a.ope_cd = b.ope_cd
        and a.merch_id = b.merch_id
        and a.merch_id in ('350200050235','350200050238')
        and a.tran_stat_cd = '011'
        and a.ope_cd = '198020'
        and substring(a.pay_id, 1, 7) = b.sub_ope_id  -- 注意：PostgreSQL使用1开始索引
        and a.local_dt between '20250701' and '20250731'
union
    select
        a.local_dt,
        a.ope_cd,
        a.merch_id,
        '' as pay_id,
        b.prdt_nm as str31,
        a.tran_at,
        a.tran_cd
    from
        tb_int_txn_log_temp a,
        tb_merch_ope b
    where
        a.ope_cd = b.ope_cd
        and a.merch_id = b.merch_id
        and a.merch_id in ('350200050235')
        and a.tran_stat_cd = '011'
        and a.ope_cd <> '198020'
        and a.unite_clr_dt between '20250701' and '20250731'
    ) q
group by
    grouping sets ( (q.local_dt,
    concat(q.ope_cd, '_', q.merch_id),
    q.pay_id,
    q.str31 ),
    rollup(q.local_dt,
    concat(q.ope_cd, '_', q.merch_id)) )
order by q.local_dt;
