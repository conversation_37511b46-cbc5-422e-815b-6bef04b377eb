<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xm.xzp.mapper.IntTxnLogMapper"><resultMap id="IntTxnLogResultMap" type="com.xm.xzp.model.vo.IntTxnLogResultVo">
    <result column="tran_dt" property="tranDt"/>
    <result column="local_tm" property="localTm"/>
    <result column="tran_inst_id" property="tranInstId"/>
    <result column="tlr_id" property="tlrId"/>
    <result column="tran_cd" property="tranCd"/>
    <result column="oth_msg7_tx" property="othMsg7Tx"/>
    <result column="ope_cd" property="opeCd"/>
    <result column="ope_nm" property="opeNm"/>
    <result column="merch_id" property="merchId"/>
    <result column="merch_nm" property="merchNm"/>
    <result column="sub_ope_cd" property="subOpeCd"/>
    <result column="sub_ope_nm" property="subOpeNm"/>
    <result column="cstm_id" property="cstmId"/>
    <result column="acc_card_id" property="accCardId"/>
    <result column="tran_at" property="tranAt"/>
    <result column="invoice_num" property="invoiceNum"/>
    <result column="pay_id" property="payId"/>
    <result column="tran_stat_cd" property="tranStatCd"/>
    <result column="rsp_cd" property="rspCd"/>
    <result column="oth_pr_msg3" property="othPrMsg3"/>
    <result column="log_id" property="logId"/>
</resultMap>

    <select id="selectIntTxnLogList" resultMap="IntTxnLogResultMap">
        SELECT
        t.tran_dt,
        t.local_tm,
        t.tran_inst_id,
        t.tlr_id,
        t.tran_cd,
        t.oth_msg7_tx,
        t.ope_cd,
        o.ope_nm,
        t.merch_id,
        m.prdt_nm as merch_nm,
        t.sub_ope_cd,
        ms.sub_ope_nm,
        t.cstm_id,
        t.acc_card_id,
        t.tran_at,
        t.invoice_num,
        t.pay_cd,
        t.tran_stat_cd,
        t.rsp_cd,
        t.oth_pr_msg3,
        t.log_id,
        t.pay_id
        FROM
        tb_int_txn_log t
        LEFT JOIN tb_merch_ope m ON t.merch_id = m.merch_id
            AND t.ope_cd = m.ope_cd
        LEFT JOIN tb_ope_cd o ON t.ope_cd = o.ope_cd
        LEFT JOIN tb_merch_sub_ope ms ON t.merch_id = ms.merch_id
            AND t.ope_cd = ms.ope_cd
            AND t.sub_ope_cd = ms.sub_ope_id
        <where>
            <if test="query.startTime != null and query.startTime != ''
                  and query.endTime != null and query.endTime != ''">
                AND t.tran_dt BETWEEN #{query.startTime} AND #{query.endTime}
            </if>
            <if test="query.merchId != null and query.merchId != ''">
                AND t.merch_id = #{query.merchId}
            </if>
            <if test="query.opeCd != null and query.opeCd != ''">
                AND t.ope_cd = #{query.opeCd}
            </if>
            <if test="query.payId != null and query.payId != ''">
                AND t.pay_id = #{query.payId}
            </if>
            <if test="query.userCd != null and query.userCd != ''
                  and query.subOpeCd != null and query.subOpeCd != ''">
                AND t.pay_id = CONCAT(#{query.subOpeCd}, #{query.userCd})
            </if>
            <if test="query.accCardId != null and query.accCardId != ''">
                AND t.acc_card_id = #{query.accCardId}
            </if>
        </where>
        ORDER BY t.tran_dt DESC, t.local_tm DESC
    </select>

    <!-- 查询缴费明细 -->
    <select id="queryTxnLogDetail" parameterType="com.xm.xzp.model.vo.IntTxnLogVo" resultType="java.util.HashMap">
        <![CDATA[
        select
            q.local_dt as setdate,
            case
                when grouping(concat(q.ope_cd, '_', q.merch_id))= 0
                    and grouping(q.pay_id)= 1 then '小计'
                else concat(q.ope_cd, '_', q.merch_id)
                end str,
            q.pay_id as str30,
            q.str31,
            count(q.tran_at) num,
            sum(q.tran_at) amt
        from
            (
                select
                    a.local_dt,
                    a.ope_cd,
                    a.merch_id,
                    substring(a.pay_id, 1, 7) pay_id,
                    b.sub_ope_nm as str31,
                    a.tran_at,
                    a.tran_cd
                from
                    tb_int_txn_log_temp a,
                    tb_merch_sub_ope b
                where
                    a.ope_cd = b.ope_cd
                  and a.merch_id = b.merch_id
                  and a.merch_id in ('350200050235','350200050238')
                  and a.tran_stat_cd = '011'
                  and a.ope_cd = '198020'
                  and substring(a.pay_id, 1, 7) = b.sub_ope_id
                  and a.local_dt between #{query.startTime} and #{query.endTime}
                union
                select
                    a.local_dt,
                    a.ope_cd,
                    a.merch_id,
                    '' as pay_id,
                    b.prdt_nm as str31,
                    a.tran_at,
                    a.tran_cd
                from
                    tb_int_txn_log_temp a,
                    tb_merch_ope b
                where
                    a.ope_cd = b.ope_cd
                  and a.merch_id = b.merch_id
                  and a.merch_id in ('350200050235')
                  and a.tran_stat_cd = '011'
                  and a.ope_cd <> '198020'
                  and a.unite_clr_dt between #{query.startTime} and #{query.endTime}
            ) q
        group by
            grouping sets ( (q.local_dt,
                             concat(q.ope_cd, '_', q.merch_id),
                             q.pay_id,
                             q.str31 ),
            rollup(q.local_dt,
                            concat(q.ope_cd, '_', q.merch_id)) )
        order by q.local_dt
        ]]>
    </select>

    <!-- Alternative query implementation - commented out
    <select id="queryTxnLogDetailAlternative" parameterType="com.xm.xzp.model.vo.IntTxnLogVo" resultType="java.util.HashMap">
        <![CDATA[
        select
            a.tran_dt as setdate,
            case when grouping(concat(a.ope_cd,'_',a.merch_id))=0 and grouping(substring(a.pay_id,0,7))=1 then '小计' else concat(a.ope_cd,'_',a.merch_id) end str,
			substring(a.pay_id,0,7) as str30,
			b.sub_ope_nm as str31,
			count(a.tran_at) num,
			sum(a.tran_at) amt
		from
			tb_int_txn_log_temp a,
			tb_merch_sub_ope b
		where
			a.ope_cd = b.ope_cd
		and a.merch_id = b.merch_id
		and a.merch_id in ('350200050235','350200050238')
		and substring(a.pay_id,0,7) = b.sub_ope_id
		group by grouping sets
					(
						(a.tran_dt,
						concat(a.ope_cd,'_',a.merch_id),
						substring(a.pay_id,0,7),
						b.sub_ope_nm
						),
						rollup(a.tran_dt,concat(a.ope_cd,'_',a.merch_id))
					)
		having a.tran_dt = #tran_dt#
        ]]>
    </select>
    -->

<!--        <![CDATA[select-->
<!--                     q.local_dt as setdate,-->
<!--                     case-->
<!--                         when grouping(concat(q.ope_cd, '_', q.merch_id))= 0-->
<!--                             and grouping(q.pay_id)= 1 then '小计'-->
<!--                         else concat(q.ope_cd, '_', q.merch_id)-->
<!--                         end str,-->
<!--                     q.pay_id as str30,-->
<!--                     q.str31,-->
<!--                     count(q.tran_at) num,-->
<!--                     sum(q.tran_at) amt-->
<!--                 from-->
<!--                     (-->
<!--                         select-->
<!--                             a.local_dt,-->
<!--                             a.ope_cd,-->
<!--                             a.merch_id,-->
<!--                             substring(a.pay_id, 0, 7) pay_id,-->
<!--                             b.sub_ope_nm as str31,-->
<!--                             a.tran_at,-->
<!--                             a.tran_id-->
<!--                         from-->
<!--                             tb_int_txn_log_temp a,-->
<!--                             tb_merch_sub_ope b-->
<!--                         where-->
<!--                             a.ope_cd = b.ope_cd-->
<!--                           and a.merch_id = b.merch_id-->
<!--                           and a.merch_id in ('350200050235','350200050238')-->
<!--                           and a.tran_stat_cd = '011'-->
<!--                           and a.ope_cd = '198020'-->
<!--                           and substring(a.pay_id, 0, 7) = b.sub_ope_id-->
<!--                           and a.local_dt between #bng_date# and #end_date#-->
<!--                         union-->
<!--                         select-->
<!--                             a.local_dt,-->
<!--                             a.ope_cd,-->
<!--                             a.merch_id,-->
<!--                             '' as pay_id,-->
<!--                             b.prdt_nm as str31,-->
<!--                             a.tran_at,-->
<!--                             a.tran_id-->
<!--                         from-->
<!--                             tb_int_txn_log_temp a,-->
<!--                             tb_merch_ope b-->
<!--                         where-->
<!--                             a.ope_cd = b.ope_cd-->
<!--                           and a.merch_id = b.merch_id-->
<!--                           and a.merch_id in ('350200050235')-->
<!--                           and a.tran_stat_cd = '011'-->
<!--                           and a.ope_cd <> '198020'-->
<!--                           and a.unite_clr_dt between #bng_date# and #end_date# ) q-->
<!--                 group by-->
<!--                     grouping sets ( (q.local_dt,-->
<!--                                      concat(q.ope_cd, '_', q.merch_id),-->
<!--                                      q.pay_id,-->
<!--                                      q.str31 ),-->
<!--                     rollup(q.local_dt,-->
<!--                                     concat(q.ope_cd, '_', q.merch_id)) )-->
<!--                 order by q.local_dt-->

<!--        ]]>-->
<!--    </select>-->

</mapper>